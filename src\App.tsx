import React, { useState } from 'react';
import {
  Text<PERSON>ield,
  <PERSON>,
  Typo<PERSON>,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Select,
  MenuItem,
  InputLabel,
  Checkbox,
  FormGroup,
  Paper,
  Container,
  Divider,
  Alert
} from "@mui/material";

interface FormData {
  // General Information
  companyName: string;

  // Energy Consumption
  energyConsumptionType: string;
  renewableEnergyElements: string[];
  nonRenewableEnergyElements: string[];
  windElectricity: string;
  solarElectricity: string;
  otherRenewableElectricity: string;
  gridElectricityConsumed: string;
  fuelConsumed: string;
  otherEnergyConsumed: string;
  totalEnergyConsumed: string;
  gridEnergyPercentage: string;
  renewablePercentage: string;
  numberOfEmployees: string;
  energyPerEmployee: string;

  // Water Consumption
  waterSource: string[];
  totalWaterWithdrawn: string;
  totalWaterConsumed: string;
  waterReused: string;
  highWaterStressRegions: string;
  waterWithdrawnStress: string;
  waterConsumedStress: string;

  // Emissions
  emissionSources: string[];
  dieselUnits: string;
  dieselCO2: string;
  petrolUnits: string;
  petrolCO2: string;
  keroseneUnits: string;
  keroseneCO2: string;
  electricityUnits: string;
  electricityCO2: string;

  // Waste Management
  wasteActions: string[];
  ewasteQuantity: string;
  ewasteHazardous: string;
  paperWasteQuantity: string;
  paperWasteHazardous: string;
  foodWasteQuantity: string;
  foodWasteHazardous: string;
  paperRecycledQuantity: string;
  paperRecycledPercentage: string;
  plasticRecycledQuantity: string;
  plasticRecycledPercentage: string;
  organicWasteQuantity: string;
  landfillWaste: string;
  landfillQuantity: string;

  // GHG Emissions
  naturalGas: string;
  distillateFuelOil: string;
  gasoline: string;
  refrigerants: string;
  purchasedElectricity: string;
  heatingCooling: string;
  travel: string;
  purchasedGoods: string;
  upstreamTransportation: string;
  wasteFromOperations: string;

  // Compliance
  ehsPractices: string;
  nonCompliance: string;
  finesPenalties: string;
  nonComplianceDetails: string;
  codeOfConduct: string;
  environmentalRegulations: string;

  // Assessments
  supplierRiskAssessment: string;
  supplierAssessmentFrequency: string;
  supplierAudits: string;
  correctiveActionPlans: string;
  carbonReductionTargets: string;

  // Financial Contributions
  financialContributions: string;
  directFinancialValue: string;
  indirectFinancialValue: string;
  inKindContributions: string;
  directInKindValue: string;
  indirectInKindValue: string;
  inKindEstimation: string;
  sbtiTargets: string;
  sbtiDescription: string;
}

const sections = [
  'General Information',
  'Energy Consumption',
  'Water Consumption',
  'Emissions',
  'Waste Management',
  'GHG Emissions',
  'Compliance',
  'Assessments',
  'Financial Contributions'
];

function App() {
  const [currentSection, setCurrentSection] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    // General Information
    companyName: '',

    // Energy Consumption
    energyConsumptionType: '',
    renewableEnergyElements: [],
    nonRenewableEnergyElements: [],
    windElectricity: '',
    solarElectricity: '',
    otherRenewableElectricity: '',
    gridElectricityConsumed: '',
    fuelConsumed: '',
    otherEnergyConsumed: '',
    totalEnergyConsumed: '',
    gridEnergyPercentage: '',
    renewablePercentage: '',
    numberOfEmployees: '',
    energyPerEmployee: '',

    // Water Consumption
    waterSource: [],
    totalWaterWithdrawn: '',
    totalWaterConsumed: '',
    waterReused: '',
    highWaterStressRegions: '',
    waterWithdrawnStress: '',
    waterConsumedStress: '',

    // Emissions
    emissionSources: [],
    dieselUnits: '',
    dieselCO2: '',
    petrolUnits: '',
    petrolCO2: '',
    keroseneUnits: '',
    keroseneCO2: '',
    electricityUnits: '',
    electricityCO2: '',

    // Waste Management
    wasteActions: [],
    ewasteQuantity: '',
    ewasteHazardous: '',
    paperWasteQuantity: '',
    paperWasteHazardous: '',
    foodWasteQuantity: '',
    foodWasteHazardous: '',
    paperRecycledQuantity: '',
    paperRecycledPercentage: '',
    plasticRecycledQuantity: '',
    plasticRecycledPercentage: '',
    organicWasteQuantity: '',
    landfillWaste: '',
    landfillQuantity: '',

    // GHG Emissions
    naturalGas: '',
    distillateFuelOil: '',
    gasoline: '',
    refrigerants: '',
    purchasedElectricity: '',
    heatingCooling: '',
    travel: '',
    purchasedGoods: '',
    upstreamTransportation: '',
    wasteFromOperations: '',

    // Compliance
    ehsPractices: '',
    nonCompliance: '',
    finesPenalties: '',
    nonComplianceDetails: '',
    codeOfConduct: '',
    environmentalRegulations: '',

    // Assessments
    supplierRiskAssessment: '',
    supplierAssessmentFrequency: '',
    supplierAudits: '',
    correctiveActionPlans: '',
    carbonReductionTargets: '',

    // Financial Contributions
    financialContributions: '',
    directFinancialValue: '',
    indirectFinancialValue: '',
    inKindContributions: '',
    directInKindValue: '',
    indirectInKindValue: '',
    inKindEstimation: '',
    sbtiTargets: '',
    sbtiDescription: ''
  });

  const [submitted, setSubmitted] = useState(false);

  const handleInputChange = (field: keyof FormData) => (event: any) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCheckboxChange = (field: keyof FormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
  };

  const handleMultiSelectChange = (field: keyof FormData, option: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => {
      const currentArray = prev[field] as string[];
      return {
        ...prev,
        [field]: event.target.checked
          ? [...currentArray, option]
          : currentArray.filter(item => item !== option)
      };
    });
  };

  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    console.log('Form submitted:', formData);
    setSubmitted(true);
  };

  const resetForm = () => {
    setCurrentSection(0);
    setFormData({
      // General Information
      companyName: '',

      // Energy Consumption
      energyConsumptionType: '',
      renewableEnergyElements: [],
      nonRenewableEnergyElements: [],
      windElectricity: '',
      solarElectricity: '',
      otherRenewableElectricity: '',
      gridElectricityConsumed: '',
      fuelConsumed: '',
      otherEnergyConsumed: '',
      totalEnergyConsumed: '',
      gridEnergyPercentage: '',
      renewablePercentage: '',
      numberOfEmployees: '',
      energyPerEmployee: '',

      // Water Consumption
      waterSource: [],
      totalWaterWithdrawn: '',
      totalWaterConsumed: '',
      waterReused: '',
      highWaterStressRegions: '',
      waterWithdrawnStress: '',
      waterConsumedStress: '',

      // Emissions
      emissionSources: [],
      dieselUnits: '',
      dieselCO2: '',
      petrolUnits: '',
      petrolCO2: '',
      keroseneUnits: '',
      keroseneCO2: '',
      electricityUnits: '',
      electricityCO2: '',

      // Waste Management
      wasteActions: [],
      ewasteQuantity: '',
      ewasteHazardous: '',
      paperWasteQuantity: '',
      paperWasteHazardous: '',
      foodWasteQuantity: '',
      foodWasteHazardous: '',
      paperRecycledQuantity: '',
      paperRecycledPercentage: '',
      plasticRecycledQuantity: '',
      plasticRecycledPercentage: '',
      organicWasteQuantity: '',
      landfillWaste: '',
      landfillQuantity: '',

      // GHG Emissions
      naturalGas: '',
      distillateFuelOil: '',
      gasoline: '',
      refrigerants: '',
      purchasedElectricity: '',
      heatingCooling: '',
      travel: '',
      purchasedGoods: '',
      upstreamTransportation: '',
      wasteFromOperations: '',

      // Compliance
      ehsPractices: '',
      nonCompliance: '',
      finesPenalties: '',
      nonComplianceDetails: '',
      codeOfConduct: '',
      environmentalRegulations: '',

      // Assessments
      supplierRiskAssessment: '',
      supplierAssessmentFrequency: '',
      supplierAudits: '',
      correctiveActionPlans: '',
      carbonReductionTargets: '',

      // Financial Contributions
      financialContributions: '',
      directFinancialValue: '',
      indirectFinancialValue: '',
      inKindContributions: '',
      directInKindValue: '',
      indirectInKindValue: '',
      inKindEstimation: '',
      sbtiTargets: '',
      sbtiDescription: ''
    });
    setSubmitted(false);
  };

  const renderGeneralInformation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        General Information
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography>

      <TextField
        fullWidth
        label="Name of the Company"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderEnergyConsumption = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Efficient consumption of Energy
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        *Energy consumption to be calculated in KwH. Provide details of energy consumption based on:
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }}>
        <FormLabel component="legend">Your energy consumption is based on?</FormLabel>
        <RadioGroup
          value={formData.energyConsumptionType}
          onChange={handleInputChange('energyConsumptionType')}
        >
          <FormControlLabel value="renewable" control={<Radio />} label="Renewable Energy" />
          <FormControlLabel value="non-renewable" control={<Radio />} label="Non-renewable energy" />
        </RadioGroup>
      </FormControl>

      <FormControl component="fieldset" sx={{ mb: 3 }}>
        <FormLabel component="legend">Renewable energy is based on which elements?</FormLabel>
        <FormGroup>
          {['Wind', 'Solar', 'Hydropower', 'Biomass', 'Geothermal Energy', 'Ocean Energy', 'Other'].map((element) => (
            <FormControlLabel
              key={element}
              control={
                <Checkbox
                  checked={formData.renewableEnergyElements.includes(element)}
                  onChange={handleMultiSelectChange('renewableEnergyElements', element)}
                />
              }
              label={element}
            />
          ))}
        </FormGroup>
      </FormControl>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Quantity of Electricity generated from Wind"
          value={formData.windElectricity}
          onChange={handleInputChange('windElectricity')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
        <TextField
          label="Quantity of Electricity generated from Solar"
          value={formData.solarElectricity}
          onChange={handleInputChange('solarElectricity')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
      </Box>

      <TextField
        fullWidth
        label="Quantity of Electricity generated from other renewable sources"
        value={formData.otherRenewableElectricity}
        onChange={handleInputChange('otherRenewableElectricity')}
        variant="outlined"
        sx={{ mb: 2 }}
      />

      <TextField
        fullWidth
        label="Quantity of Grid Electricity Consumed (KwH)"
        value={formData.gridElectricityConsumed}
        onChange={handleInputChange('gridElectricityConsumed')}
        variant="outlined"
        sx={{ mb: 2 }}
      />

      <TextField
        fullWidth
        label="Quantity of fuel consumed"
        value={formData.fuelConsumed}
        onChange={handleInputChange('fuelConsumed')}
        variant="outlined"
        helperText="Include petrol, diesel, kerosene (and any other fuel) used for vehicles, generators and other machinery"
        sx={{ mb: 2 }}
      />

      <TextField
        fullWidth
        label="Total energy consumed"
        value={formData.totalEnergyConsumed}
        onChange={handleInputChange('totalEnergyConsumed')}
        variant="outlined"
        sx={{ mb: 2 }}
      />

      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Percentage of energy sourced from the grid</InputLabel>
        <Select
          value={formData.gridEnergyPercentage}
          label="Percentage of energy sourced from the grid"
          onChange={handleInputChange('gridEnergyPercentage')}
        >
          <MenuItem value="10-20%">10-20%</MenuItem>
          <MenuItem value="20-40%">20-40%</MenuItem>
          <MenuItem value="40-50%">40-50%</MenuItem>
          <MenuItem value="50-60%">50-60%</MenuItem>
          <MenuItem value="60% and above">60% and above</MenuItem>
        </Select>
      </FormControl>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Number of Employees for the period"
          value={formData.numberOfEmployees}
          onChange={handleInputChange('numberOfEmployees')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
        <TextField
          label="Energy Use per employee"
          value={formData.energyPerEmployee}
          onChange={handleInputChange('energyPerEmployee')}
          variant="outlined"
          helperText="Total energy consumed / number of employees"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
      </Box>
    </Box>
  );

  if (submitted) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
          <Alert severity="success" sx={{ mb: 3 }}>
            Thank you for submitting the SLASSCOM Baseline Matrix form!
          </Alert>
          <Typography variant="h5" gutterBottom>
            Form Submitted Successfully
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            We have received your environmental baseline data and will process it accordingly.
          </Typography>
          <Button variant="contained" onClick={resetForm}>
            Submit Another Response
          </Button>
        </Paper>
      </Container>
    );
  }

  const renderWaterConsumption = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Efficient consumption of Water
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        *Water consumption to be calculated in m³. Provide details of water consumption based on:
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }}>
        <FormLabel component="legend">Source of water</FormLabel>
        <FormGroup>
          {['Main supply (Eg: municipal supply)', 'Private Providers', 'Rain water', 'Ground water', 'Surface water'].map((source) => (
            <FormControlLabel
              key={source}
              control={
                <Checkbox
                  checked={formData.waterSource.includes(source)}
                  onChange={handleMultiSelectChange('waterSource', source)}
                />
              }
              label={source}
            />
          ))}
        </FormGroup>
      </FormControl>

      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Water usage for day-to-day operations
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Total water withdrawn (m³)"
          value={formData.totalWaterWithdrawn}
          onChange={handleInputChange('totalWaterWithdrawn')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
        <TextField
          label="Total water consumed (m³)"
          value={formData.totalWaterConsumed}
          onChange={handleInputChange('totalWaterConsumed')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
      </Box>

      <TextField
        fullWidth
        label="Water re-used through treatment facilities (m³)"
        value={formData.waterReused}
        onChange={handleInputChange('waterReused')}
        variant="outlined"
        sx={{ mb: 3 }}
      />

      <FormControl component="fieldset" sx={{ mb: 3 }}>
        <FormLabel component="legend">Do you operate in Regions with High or Extremely High Baseline Water Stress?</FormLabel>
        <RadioGroup
          value={formData.highWaterStressRegions}
          onChange={handleInputChange('highWaterStressRegions')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>

      {formData.highWaterStressRegions === 'yes' && (
        <Box>
          <Typography variant="h6" gutterBottom>
            Percentage for each in regions with High or Extremely High Baseline Water Stress
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
            <TextField
              label="Total water withdrawn (%)"
              value={formData.waterWithdrawnStress}
              onChange={handleInputChange('waterWithdrawnStress')}
              variant="outlined"
              sx={{ flex: '1 1 300px', minWidth: '250px' }}
            />
            <TextField
              label="Total water consumed (%)"
              value={formData.waterConsumedStress}
              onChange={handleInputChange('waterConsumedStress')}
              variant="outlined"
              sx={{ flex: '1 1 300px', minWidth: '250px' }}
            />
          </Box>
        </Box>
      )}
    </Box>
  );

  const renderEmissions = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Minimizing discharge of refuse - Emissions
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }}>
        <FormLabel component="legend">Emissions from</FormLabel>
        <FormGroup>
          {['Fuel', 'Grid power', 'Air travel', 'Road travel', 'Capital goods'].map((source) => (
            <FormControlLabel
              key={source}
              control={
                <Checkbox
                  checked={formData.emissionSources.includes(source)}
                  onChange={handleMultiSelectChange('emissionSources', source)}
                />
              }
              label={source}
            />
          ))}
        </FormGroup>
      </FormControl>

      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Direct and Indirect GHG Emissions
      </Typography>

      {/* Diesel */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Diesel
      </Typography>
      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Units consumed (Litres)"
          value={formData.dieselUnits}
          onChange={handleInputChange('dieselUnits')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
        <TextField
          label="Total CO2 emission (TCO2)"
          value={formData.dieselCO2}
          onChange={handleInputChange('dieselCO2')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
      </Box>

      {/* Petrol */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Petrol
      </Typography>
      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Units consumed (Litres)"
          value={formData.petrolUnits}
          onChange={handleInputChange('petrolUnits')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
        <TextField
          label="Total CO2 emission (TCO2)"
          value={formData.petrolCO2}
          onChange={handleInputChange('petrolCO2')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
      </Box>

      {/* Electricity */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Electricity
      </Typography>
      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Units consumed (KwH)"
          value={formData.electricityUnits}
          onChange={handleInputChange('electricityUnits')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
        <TextField
          label="Total CO2 emission (KG)"
          value={formData.electricityCO2}
          onChange={handleInputChange('electricityCO2')}
          variant="outlined"
          sx={{ flex: '1 1 300px', minWidth: '250px' }}
        />
      </Box>
    </Box>
  );

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 0:
        return renderGeneralInformation();
      case 1:
        return renderEnergyConsumption();
      case 2:
        return renderWaterConsumption();
      case 3:
        return renderEmissions();
      default:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {sections[currentSection]}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              This section is under development. Please navigate to other sections.
            </Typography>
          </Box>
        );
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center">
          SLASSCOM - Baseline Matrix
        </Typography>
        <Typography variant="subtitle1" gutterBottom align="center" color="text.secondary">
          Environmental Sustainability Assessment Form
        </Typography>

        {/* Progress Indicator */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 1 }}>
            Section {currentSection + 1} of {sections.length}: {sections[currentSection]}
          </Typography>
          <Box sx={{ width: '100%', bgcolor: 'grey.300', borderRadius: 1, height: 8 }}>
            <Box
              sx={{
                width: `${((currentSection + 1) / sections.length) * 100}%`,
                bgcolor: 'primary.main',
                height: 8,
                borderRadius: 1,
                transition: 'width 0.3s ease'
              }}
            />
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box component="form" onSubmit={handleSubmit} noValidate>
          {renderCurrentSection()}
        </Box>

        {/* Navigation Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button
            variant="outlined"
            onClick={handlePrevious}
            disabled={currentSection === 0}
            sx={{ minWidth: 120 }}
          >
            Previous
          </Button>

          <Box sx={{ display: 'flex', gap: 2 }}>
            {currentSection === sections.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                sx={{ minWidth: 120 }}
              >
                Submit Form
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                sx={{ minWidth: 120 }}
              >
                Next
              </Button>
            )}

            <Button
              variant="outlined"
              onClick={resetForm}
              sx={{ minWidth: 120 }}
            >
              Reset
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
}

export default App;